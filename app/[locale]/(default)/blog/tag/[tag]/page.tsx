import { Metadata } from 'next';
import { getBlogPostsByTag } from '@/lib/blog';
import { BlogCard } from '@/components/blog/blog-card';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Tag } from 'lucide-react';
import Link from 'next/link';

interface TagPageProps {
  params: Promise<{ locale: string; tag: string }>;
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string; tag: string }>;
}): Promise<Metadata> {
  const { locale, tag } = await params;
  
  const title = locale === 'zh' 
    ? `标签: ${tag} - 博客` 
    : `Tag: ${tag} - Blog`;
  
  const description = locale === 'zh'
    ? `查看所有标记为 "${tag}" 的文章`
    : `View all posts tagged with "${tag}"`;

  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/blog/tag/${tag}`;
  if (locale !== 'en') {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/blog/tag/${tag}`;
  }

  return {
    title,
    description,
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: {
      title,
      description,
      type: 'website',
      url: canonicalUrl,
    },
  };
}

export default async function TagPage({ params }: TagPageProps) {
  const { locale, tag } = await params;
  const posts = await getBlogPostsByTag(tag, locale);
  const backUrl = locale === 'en' ? '/blog' : `/${locale}/blog`;

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 返回链接 */}
        <div className="mb-6">
          <Link 
            href={backUrl}
            className="inline-flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            {locale === 'zh' ? '返回博客' : 'Back to Blog'}
          </Link>
        </div>

        {/* 标签标题 */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center gap-3 mb-4">
            <Tag className="w-6 h-6 text-muted-foreground" />
            <Badge variant="outline" className="text-lg px-4 py-2">
              {tag}
            </Badge>
          </div>
          <h1 className="text-4xl font-bold tracking-tight mb-4">
            {locale === 'zh' ? `标签: ${tag}` : `Tag: ${tag}`}
          </h1>
          <p className="text-lg text-muted-foreground">
            {locale === 'zh' 
              ? `查看所有标记为 "${tag}" 的文章`
              : `View all posts tagged with "${tag}"`
            }
          </p>
          <p className="text-sm text-muted-foreground mt-2">
            {posts.length} {locale === 'zh' ? '篇文章' : 'posts'}
          </p>
        </div>

        {/* 文章列表 */}
        {posts.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-muted-foreground">
              {locale === 'zh' ? '该标签下暂无文章' : 'No posts with this tag yet'}
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {posts.map((post) => (
              <BlogCard
                key={post.slug}
                post={post}
                locale={locale}
                variant="default"
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
