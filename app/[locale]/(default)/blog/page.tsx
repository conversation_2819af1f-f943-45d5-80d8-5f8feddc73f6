import { Suspense } from 'react';
import { Metadata } from 'next';
import { getBlogList, getBlogCategories } from '@/lib/blog';
import { BlogCard } from '@/components/blog/blog-card';
import { Badge } from '@/components/ui/badge';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import Link from 'next/link';

interface BlogPageProps {
  params: Promise<{ locale: string }>;
  searchParams: Promise<{ category?: string; tag?: string; page?: string }>;
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  
  const title = locale === 'zh' ? '博客 - AI ASMR 生成器' : 'Blog - AI ASMR Generator';
  const description = locale === 'zh' 
    ? '探索 AI ASMR 生成器的最新功能、使用技巧和行业洞察'
    : 'Explore the latest features, tips, and insights about AI ASMR Generator';

  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/blog`;
  if (locale !== 'en') {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/blog`;
  }

  return {
    title,
    description,
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: {
      title,
      description,
      type: 'website',
      url: canonicalUrl,
    },
  };
}

function BlogListSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {Array.from({ length: 6 }).map((_, i) => (
        <Card key={i} className="h-full">
          <div className="aspect-[16/9] bg-muted animate-pulse rounded-t-lg" />
          <CardHeader>
            <Skeleton className="h-4 w-20 mb-2" />
            <Skeleton className="h-6 w-full" />
            <Skeleton className="h-6 w-3/4" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-4 w-full mb-2" />
            <Skeleton className="h-4 w-full mb-2" />
            <Skeleton className="h-4 w-2/3 mb-4" />
            <div className="flex justify-between">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-16" />
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

async function BlogContent({ locale, searchParams }: { 
  locale: string; 
  searchParams: { category?: string; tag?: string; page?: string } 
}) {
  const posts = await getBlogList(locale);
  const categories = await getBlogCategories(locale);

  // 过滤文章
  let filteredPosts = posts;
  if (searchParams.category) {
    filteredPosts = posts.filter(post => post.category === searchParams.category);
  }
  if (searchParams.tag) {
    filteredPosts = posts.filter(post => post.tags.includes(searchParams.tag));
  }

  // 分离精选文章
  const featuredPosts = filteredPosts.filter(post => post.featured);
  const regularPosts = filteredPosts.filter(post => !post.featured);

  // 获取所有标签
  const allTags = Array.from(
    new Set(posts.flatMap(post => post.tags))
  ).sort();

  const currentCategory = searchParams.category;
  const currentTag = searchParams.tag;

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题 */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold tracking-tight mb-4">
            {locale === 'zh' ? '博客' : 'Blog'}
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            {locale === 'zh' 
              ? '探索 AI ASMR 生成器的最新功能、使用技巧和行业洞察'
              : 'Explore the latest features, tips, and insights about AI ASMR Generator'
            }
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* 主内容区域 */}
          <main className="lg:col-span-3">
            {/* 当前过滤器显示 */}
            {(currentCategory || currentTag) && (
              <div className="mb-6 p-4 bg-muted/50 rounded-lg">
                <div className="flex items-center gap-2 text-sm">
                  <span className="text-muted-foreground">
                    {locale === 'zh' ? '当前筛选：' : 'Filtered by:'}
                  </span>
                  {currentCategory && (
                    <Badge variant="secondary">
                      {locale === 'zh' ? '分类：' : 'Category: '}{currentCategory}
                    </Badge>
                  )}
                  {currentTag && (
                    <Badge variant="secondary">
                      {locale === 'zh' ? '标签：' : 'Tag: '}{currentTag}
                    </Badge>
                  )}
                  <Link 
                    href={locale === 'en' ? '/blog' : `/${locale}/blog`}
                    className="text-primary hover:underline ml-2"
                  >
                    {locale === 'zh' ? '清除筛选' : 'Clear filters'}
                  </Link>
                </div>
              </div>
            )}

            {/* 精选文章 */}
            {featuredPosts.length > 0 && !currentCategory && !currentTag && (
              <section className="mb-12">
                <h2 className="text-2xl font-bold mb-6">
                  {locale === 'zh' ? '精选文章' : 'Featured Posts'}
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {featuredPosts.slice(0, 2).map((post) => (
                    <BlogCard
                      key={post.slug}
                      post={post}
                      locale={locale}
                      variant="featured"
                    />
                  ))}
                </div>
              </section>
            )}

            {/* 最新文章 */}
            <section>
              <h2 className="text-2xl font-bold mb-6">
                {currentCategory || currentTag 
                  ? (locale === 'zh' ? '筛选结果' : 'Filtered Results')
                  : (locale === 'zh' ? '最新文章' : 'Latest Posts')
                }
              </h2>
              
              {filteredPosts.length === 0 ? (
                <div className="text-center py-12">
                  <p className="text-muted-foreground">
                    {locale === 'zh' ? '暂无文章' : 'No posts found'}
                  </p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6">
                  {(currentCategory || currentTag ? filteredPosts : regularPosts).map((post) => (
                    <BlogCard
                      key={post.slug}
                      post={post}
                      locale={locale}
                      variant="default"
                    />
                  ))}
                </div>
              )}
            </section>
          </main>

          {/* 侧边栏 */}
          <aside className="lg:col-span-1">
            <div className="sticky top-8 space-y-6">
              {/* 分类 */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">
                    {locale === 'zh' ? '分类' : 'Categories'}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <Link
                    href={locale === 'en' ? '/blog' : `/${locale}/blog`}
                    className={`block p-2 rounded-md transition-colors ${
                      !currentCategory 
                        ? 'bg-primary text-primary-foreground' 
                        : 'hover:bg-muted'
                    }`}
                  >
                    {locale === 'zh' ? '全部' : 'All'}
                  </Link>
                  {Object.entries(categories).map(([key, category]) => {
                    const categoryUrl = locale === 'en' 
                      ? `/blog?category=${key}` 
                      : `/${locale}/blog?category=${key}`;
                    
                    return (
                      <Link
                        key={key}
                        href={categoryUrl}
                        className={`block p-2 rounded-md transition-colors ${
                          currentCategory === key 
                            ? 'bg-primary text-primary-foreground' 
                            : 'hover:bg-muted'
                        }`}
                      >
                        {category.name}
                      </Link>
                    );
                  })}
                </CardContent>
              </Card>

              {/* 标签云 */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">
                    {locale === 'zh' ? '标签' : 'Tags'}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {allTags.map((tag) => {
                      const tagUrl = locale === 'en' 
                        ? `/blog?tag=${tag}` 
                        : `/${locale}/blog?tag=${tag}`;
                      
                      return (
                        <Link key={tag} href={tagUrl}>
                          <Badge 
                            variant={currentTag === tag ? "default" : "outline"}
                            className="cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors"
                          >
                            {tag}
                          </Badge>
                        </Link>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            </div>
          </aside>
        </div>
      </div>
    </div>
  );
}

export default async function BlogPage({ params, searchParams }: BlogPageProps) {
  const { locale } = await params;
  const resolvedSearchParams = await searchParams;

  return (
    <Suspense fallback={<BlogListSkeleton />}>
      <BlogContent locale={locale} searchParams={resolvedSearchParams} />
    </Suspense>
  );
}
